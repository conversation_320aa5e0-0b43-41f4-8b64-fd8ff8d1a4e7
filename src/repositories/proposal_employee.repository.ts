import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FindEmployeesProposalsFilters } from 'src/types/find-employee-proposals-filters';
import { ProposalEmployeeEntity } from '../entities/proposals-employees.entity';

@Injectable()
export class ProposalEmployeeRepository {
  constructor(
    @InjectRepository(ProposalEmployeeEntity)
    public repository: Repository<ProposalEmployeeEntity>
  ) {}

  public async findAllProposalEmployeeProposals({
    employeeUuid,
    year
  }: FindEmployeesProposalsFilters): Promise<ProposalEmployeeEntity> {
    const query = this.repository.createQueryBuilder('employee');

    this.applyFilters({ employeeUuid, year }, query);

    const employee = await query
      .leftJoinAndSelect('employee.proposals', 'proposal')
      .leftJoinAndSelect('proposal.targets', 'targets')
      .leftJoinAndSelect('targets.children', 'children')
      .leftJoinAndSelect('targets.deliverable', 'deliverable')
      .leftJoinAndSelect('deliverable.deliverableType', 'deliverableType')
      .leftJoinAndSelect('children.deliverable', 'childDeliverable')
      .leftJoinAndSelect('childDeliverable.deliverableType', 'childDeliverableType')
      .leftJoinAndSelect('targets.targetTypes', 'targetTypes')
      .select([
        'employee',
        'proposal.uid',
        'proposal.status',
        'proposal.dateStart',
        'proposal.dateEnd',
        'targets.uid',
        'targets.weight',
        'targets.scope',
        'children.uid',
        'children.weight',
        'children.scope',
        'deliverable.uid',
        'deliverable.name',
        'deliverable.businessFunction',
        'deliverable.calculationMethod',
        'deliverable.definition',
        'deliverableType.code',
        'childDeliverable.uid',
        'childDeliverable.name',
        'childDeliverable.businessFunction',
        'childDeliverable.calculationMethod',
        'childDeliverable.definition',
        'childDeliverableType.code',
        'targetTypes.type'
      ])
      .andWhere('targets.parentTarget IS NULL')
      .getOne();

    if (!employee) {
      throw new Error('Employee not found');
    }

    return employee;
  }

  private applyFilters(
    { employeeUuid, year }: FindEmployeesProposalsFilters,
    query: ReturnType<Repository<ProposalEmployeeEntity>['createQueryBuilder']>
  ) {
    if (employeeUuid) {
      query.andWhere('employee.employeeUuid = :employeeUuid', { employeeUuid: employeeUuid });
    }

    if (year) {
      query.andWhere('proposal.dateStart >= :startDate AND proposal.dateEnd <= :endDate', {
        startDate: new Date(year, 1, 1),
        endDate: new Date(year, 12, 31)
      });
    }
  }
}
